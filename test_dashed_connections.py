#!/usr/bin/env python3
"""
测试虚线连接功能
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dashed_connections():
    """测试虚线连接功能"""
    try:
        from fio_analyzer.main import FioLogAnalyzer
        import tkinter as tk
        
        print("🧪 测试虚线连接功能...")
        print("=" * 50)
        
        # 创建测试应用
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口，只测试功能
        
        app = FioLogAnalyzer(root)
        
        # 测试颜色映射功能
        test_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#unknown_color']
        print("🎨 测试新的鲜艳虚线颜色映射:")
        for color in test_colors:
            vibrant = app._get_vibrant_dash_color(color)
            print(f"   {color} -> {vibrant}")

        print("\n🎨 测试原有的淡色映射:")
        for color in test_colors:
            lighter = app._get_lighter_color(color)
            print(f"   {color} -> {lighter}")

        print("\n✅ 虚线连接功能测试完成!")
        print("📝 功能改进说明:")
        print("   🔧 修改1: 只要是不同文件就连接，不管时间间隔多少（包括0秒）")
        print("   🔧 修改2: 使用更显眼的鲜艳颜色，线宽2.0px，透明度80%")
        print("   🔧 修改3: 解决了同一时间点不同文件的断层问题")
        print("   - 悬停提示会明确标示这是连接线，非真实数据")
        print("   - 连接线会在图例中显示，可以单独控制显示/隐藏")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_dashed_connections()
    if success:
        print("\n🚀 可以运行主程序测试完整功能:")
        print("   python run_modular.py")
    else:
        print("\n❌ 测试失败，请检查代码")
