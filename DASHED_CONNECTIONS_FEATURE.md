# 🔗 虚线连接功能说明

## 📋 功能概述

在HTML合并模式下，当不同文件之间存在操作类型的空白期时，系统会自动添加虚线连接，让用户清楚地看到数据的连续性，同时明确区分真实数据和连接线。

## 🎯 解决的问题

**问题描述**: 
- 在合并模式下，当文件A的Write操作结束后，文件B的Write操作开始前存在时间间隔
- 图表会出现断开，用户无法直观看到数据的连续性
- 用户可能误以为数据丢失或处理有误

**解决方案**:
- 自动检测文件间的时间间隔
- 当间隔 > 1秒时，添加虚线连接
- 虚线使用较淡颜色和透明度，明确标示为非真实数据

## 🔧 技术实现

### 1. 核心修改文件
- `fio_analyzer/main.py` - 主要实现文件

### 2. 新增方法
```python
def _add_dashed_connections(self, fig, all_file_segments, icon, op_name):
    """添加虚线连接不同文件间的空白"""
    
def _get_lighter_color(self, color):
    """获取较淡的颜色用于虚线"""
```

### 3. 修改的方法
```python
def _create_single_merged_operation_chart(self, operation, op_name, color, chart_type, x_intervals, sample_rate):
    """创建单个操作类型的合并图表 - 增加虚线连接功能"""
```

## 📊 功能特性

### 1. 智能检测
- **时间间隔阈值**: 1.0秒（可调整）
- **自动排序**: 按时间顺序排列数据段
- **空白检测**: 自动识别需要连接的间隔

### 2. 视觉设计
- **虚线样式**: `dash='dash'`
- **颜色处理**: 使用原色的较淡版本
- **透明度**: 70% (`opacity=0.7`)
- **线宽**: 1.0px（比实线稍细）

### 3. 交互体验
- **悬停提示**: 明确标示为连接线，非真实数据
- **图例控制**: 可以单独显示/隐藏连接线
- **分组管理**: 所有连接线归为一个图例组

## 🎨 颜色映射

系统内置颜色映射，将原色转换为较淡版本：

| 原色 | 淡色 | 说明 |
|------|------|------|
| #1f77b4 | #7fb3d3 | 蓝色 → 淡蓝色 |
| #ff7f0e | #ffb366 | 橙色 → 淡橙色 |
| #2ca02c | #7dd87d | 绿色 → 淡绿色 |
| #d62728 | #e67e7e | 红色 → 淡红色 |
| #9467bd | #c4a4d6 | 紫色 → 淡紫色 |
| 其他 | #cccccc | 默认淡灰色 |

## 📝 使用说明

### 1. 启动程序
```bash
python run_modular.py
```

### 2. 操作步骤
1. 选择多个FIO日志文件
2. 选择"合并模式"
3. 点击"载入数据并开启图表"
4. 在浏览器中查看带虚线连接的图表

### 3. 图表说明
- **实线**: 真实的FIO操作数据
- **虚线**: 文件间的连接线（非真实数据）
- **图例**: 可以单独控制连接线的显示/隐藏

## 🔍 调试信息

程序会在控制台输出调试信息：

```
🔧 Write操作虚线连接: 找到 3 个数据段
   连接 file1.log -> file2.log: 间隔 5.23s
   连接 file2.log -> file3.log: 间隔 2.15s
```

## ⚙️ 配置选项

### 时间间隔阈值
在 `_add_dashed_connections` 方法中可以调整：
```python
if time_gap > 1.0:  # 1秒阈值，可以根据需要调整
```

### 透明度设置
在虚线轨迹中可以调整：
```python
opacity=0.7  # 稍微透明，表示这不是真实数据
```

## 🚀 效果展示

**修改前**:
- 文件间存在断开
- 用户无法看到数据连续性
- 可能误解为数据丢失

**修改后**:
- 虚线自动连接断开部分
- 清楚标示为连接线，非真实数据
- 保持数据的视觉连续性
- 用户体验大幅提升

## 🔄 兼容性

- ✅ 不影响其他功能
- ✅ 只在合并模式下生效
- ✅ 双轴对比模式不受影响
- ✅ 导出功能正常工作
- ✅ 所有交互功能保持不变

## 📋 测试建议

1. **基本功能测试**: 加载多个有时间间隔的文件
2. **边界测试**: 测试间隔刚好1秒的情况
3. **颜色测试**: 验证不同颜色的淡化效果
4. **交互测试**: 测试图例控制和悬停提示
5. **性能测试**: 确保大文件处理性能不受影响
