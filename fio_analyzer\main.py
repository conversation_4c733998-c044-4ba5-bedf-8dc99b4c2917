"""
FIO Log Analyzer - 主程序
模組化版本的主應用程序
"""

import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import os
import sys
import threading
import tempfile
import webbrowser
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from collections import defaultdict
import logging

# 導入自定義模組
from .data_processor import DataProcessor, FileTimeRange, AnalysisResult
from .chart_generator import PlotlyChartGenerator
from .html_templates import HTMLTemplateGenerator
from .ui_components import UIComponents

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_and_install_dependencies():
    """檢查並安裝必要的依賴"""
    required_packages = {
        'polars': 'polars',
        'plotly': 'plotly',
        'numpy': 'numpy'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"缺少必要的套件: {', '.join(missing_packages)}")
        print("請執行以下命令安裝:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

class FioLogAnalyzer:
    """主應用程序類 - 模組化版本"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("FIO Log Analyzer - Modular Edition")
        
        # 初始化 UI 組件管理器
        self.ui = UIComponents(self)
        
        # 設定主視窗 - 縮小初始大小
        self.root.geometry("1200x700")
        self.root.minsize(1000, 600)
        
        # 初始化 UI
        self._init_ui()
        
        # 綁定 UI 組件的方法到主應用程序
        self._bind_ui_methods()
    
    def _init_ui(self):
        """初始化用戶界面"""
        # 主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 創建各個區域
        self._create_top_frame()
        self._create_bottom_frame()
    
    def _create_top_frame(self):
        """創建上半部分框架"""
        self.top_frame = ttk.Frame(self.main_frame)
        self.top_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左側文件選擇區域
        self.ui.create_file_selection_area(self.top_frame)
        
        # 右側圖表控制區域
        self.ui.create_chart_control_area(self.top_frame)
    
    def _create_bottom_frame(self):
        """創建下半部分框架 - 移除說明文字"""
        # 不再創建下半部分框架，因為功能已移至瀏覽器端
        pass
    
    def _bind_ui_methods(self):
        """綁定 UI 組件的方法到主應用程序"""
        # 將主應用程序的方法綁定到 UI 組件
        self.ui.load_and_display_data_threaded = self.load_and_display_data_threaded
        self.ui.load_and_open_chart_threaded = self.load_and_open_chart_threaded  # 新增一鍵式操作
        self.ui.open_interactive_plot = self.open_interactive_plot
        self.ui.export_plot = self.export_plot
        self.ui.update_plot = self.update_plot
        # 分析功能已移至瀏覽器端，不再需要綁定

        # 重新綁定一鍵式按鈕
        if hasattr(self.ui, 'load_and_open_button'):
            self.ui.load_and_open_button.config(command=self.load_and_open_chart_threaded)

        # 重新綁定僅載入按鈕
        if hasattr(self.ui, 'load_only_button'):
            self.ui.load_only_button.config(command=self.load_and_display_data_threaded)

        # 重新綁定圖表按鈕
        if hasattr(self.ui, 'open_plot_button'):
            self.ui.open_plot_button.config(command=self.open_interactive_plot)

        if hasattr(self.ui, 'update_plot_button'):
            self.ui.update_plot_button.config(command=self.update_plot)

        if hasattr(self.ui, 'export_plot_button'):
            self.ui.export_plot_button.config(command=self.export_plot)
    
    # 數據載入方法
    def load_and_open_chart_threaded(self):
        """一鍵式操作：載入數據並開啟圖表（線程版本）"""
        def load_and_open_worker():
            try:
                # 檢查是否選擇了文件
                if not self.ui.logs:
                    self.root.after(0, lambda: self.ui.show_message("請先選擇至少一個 log 檔案！"))
                    return

                # 步驟1：載入數據
                self.root.after(0, lambda: self.ui.status_label.config(text="🔄 正在載入數據..."))
                self.load_and_display_data()

                # 步驟2：生成並開啟圖表
                self.root.after(0, lambda: self.ui.status_label.config(text="🎨 正在生成圖表..."))
                self.open_interactive_plot()

                # 完成
                self.root.after(0, lambda: self.ui.status_label.config(text="✅ 載入完成，圖表已開啟"))

            except Exception as e:
                logger.error(f"一鍵式操作錯誤: {str(e)}")
                self.root.after(0, lambda: self.ui.show_message(f"操作錯誤: {str(e)}"))
                self.root.after(0, lambda: self.ui.status_label.config(text="❌ 操作失敗"))

        thread = threading.Thread(target=load_and_open_worker, daemon=True)
        thread.start()

    def load_and_display_data_threaded(self):
        """使用線程載入數據（僅載入，不開啟圖表）"""
        def load_worker():
            try:
                self.load_and_display_data()
            except Exception as e:
                logger.error(f"載入數據錯誤: {str(e)}")
                self.root.after(0, lambda: self.ui.show_message(f"載入錯誤: {str(e)}"))

        thread = threading.Thread(target=load_worker, daemon=True)
        thread.start()

    def load_and_display_data(self):
        """載入和顯示數據"""
        if not self.ui.logs:
            self.ui.show_message("請先選擇至少一個 log 檔案！")
            return

        self.root.after(0, lambda: self.ui.status_label.config(text="開始載入數據..."))
        self.root.after(0, lambda: self.ui.progress_var.set(0))

        # 清空之前的數據
        self.ui.all_data = []
        self.ui.file_time_ranges = []

        try:
            # 載入時永遠使用完整數據，採樣只在圖表生成時進行
            # 使用多線程載入文件，但保持順序
            with ThreadPoolExecutor(max_workers=min(4, len(self.ui.logs))) as executor:
                # 創建有序的 future 列表，保持與 self.ui.logs 相同的順序
                futures = [
                    executor.submit(self.ui.data_processor.load_file_optimized, log_file, 1)
                    for log_file in self.ui.logs
                ]

                # 按順序等待每個 future 完成，確保 all_data 順序與 logs 一致
                for i, future in enumerate(futures):
                    try:
                        df = future.result()
                        self.ui.all_data.append(df)
                        progress = (i + 1) / len(self.ui.logs) * 50
                        self.root.after(0, lambda p=progress: self.ui.progress_var.set(p))
                        self.root.after(0, lambda i=i: self.ui.status_label.config(text=f"已載入 {i+1}/{len(self.ui.logs)} 個檔案"))
                        print(f"✅ 載入文件 {i}: {os.path.basename(self.ui.logs[i])}")
                    except Exception as e:
                        logger.error(f"載入文件錯誤: {str(e)}")
                        self.root.after(0, lambda e=e: self.ui.show_message(str(e)))
                        return

            # 處理時間偏移
            self._process_time_offsets()

            # 合併數據
            self.root.after(0, lambda: self.ui.status_label.config(text="合併數據..."))
            import polars as pl
            self.ui.combined_df = pl.concat(self.ui.all_data)

            # 獲取最大時間
            self.ui.max_time = self.ui.combined_df['time_sec'].max()
            self.ui.start_time = 0
            self.ui.end_time = self.ui.max_time

            self.root.after(0, lambda: self.ui.progress_var.set(100))
            self.root.after(0, lambda: self._update_time_range_label())
            self.root.after(0, lambda: self._show_file_time_ranges())
            self.root.after(0, lambda: self.ui.status_label.config(text="✅ 數據載入完成 - 可以開啟互動式圖表"))
                
        except Exception as e:
            logger.error(f"載入數據時發生錯誤: {str(e)}")
            self.root.after(0, lambda: self.ui.show_message(f"載入錯誤: {str(e)}"))

    def _process_time_offsets(self):
        """處理時間偏移"""
        self.root.after(0, lambda: self.ui.status_label.config(text="處理時間偏移..."))
        current_time_offset = 0

        for i, df in enumerate(self.ui.all_data):
            file_min_time = df['time_sec'].min()
            file_max_time = df['time_sec'].max()
            file_duration = file_max_time - file_min_time

            # 使用 Polars 進行時間偏移
            import polars as pl
            df = df.with_columns([
                pl.col('time_sec').alias('original_time'),
                (pl.col('time_sec') - file_min_time + current_time_offset).alias('time_sec')
            ])

            self.ui.all_data[i] = df

            self.ui.file_time_ranges.append(FileTimeRange(
                file=os.path.basename(self.ui.logs[i]),
                start=current_time_offset,
                end=current_time_offset + file_duration
            ))

            current_time_offset += file_duration

            progress = 50 + (i + 1) / len(self.ui.all_data) * 30
            self.root.after(0, lambda p=progress: self.ui.progress_var.set(p))

    def _update_time_range_label(self):
        """更新時間範圍標籤"""
        if hasattr(self.ui, 'time_range_label'):
            self.ui.time_range_label.config(text=f"0.00s - {self.ui.max_time:.2f}s")

    def _show_file_time_ranges(self):
        """顯示文件時間範圍"""
        if hasattr(self.ui, 'result_text'):
            self.ui.result_text.delete(1.0, tk.END)
            self.ui.result_text.insert(tk.END, "文件時間範圍:\n")
            self.ui.result_text.insert(tk.END, "=" * 50 + "\n")

            for range_info in self.ui.file_time_ranges:
                self.ui.result_text.insert(tk.END,
                    f"{range_info.file}: {range_info.start:.2f}s - {range_info.end:.2f}s "
                    f"(持續時間: {range_info.duration:.2f}s)\n")

            self.ui.result_text.insert(tk.END, "\n總時間範圍: 0.00s - {:.2f}s\n".format(self.ui.max_time))
    
    # 圖表生成方法
    def open_interactive_plot(self):
        """打開互動式圖表"""
        print("🔧 開啟互動式圖表被調用")

        if not self.ui.all_data:
            print("❌ 沒有數據")
            self.ui.show_message("請先載入數據！")
            return

        print(f"✅ 有數據，共 {len(self.ui.all_data)} 個文件")

        try:
            self.ui.status_label.config(text="🎨 正在生成互動式圖表...")

            # 獲取設置
            try:
                x_intervals = int(self.ui.x_intervals_var.get())
            except:
                x_intervals = 20

            try:
                sample_rate = int(self.ui.sample_var.get())
            except:
                sample_rate = 1

            chart_type = self.ui.chart_type_var.get()
            chart_mode = self.ui.chart_mode_var.get()

            print(f"🔧 圖表設置: 類型={chart_type}, 模式={chart_mode}, X軸區間={x_intervals}, 採樣率={sample_rate}")

            # 檢查是否為操作類型分離對比模式
            if chart_mode == "雙軸對比模式":
                print("🔧 創建操作類型分離對比圖表")
                figures = self._create_dual_axis_chart(chart_type, x_intervals, sample_rate)

                # 檢查圖表是否成功生成
                if not figures or len(figures) == 0:
                    print("❌ 圖表生成失敗")
                    self.ui.show_message("圖表生成失敗！")
                    return

                print(f"✅ 生成了 {len(figures)} 個操作類型對比圖表")

                # 🔧 處理多圖表的HTML生成
                self._create_multi_chart_html(figures, chart_type, x_intervals, sample_rate)
                return

            else:
                print("🔧 創建合併模式多圖表")
                # 🔧 新增：合併模式也生成多圖表（按操作類型分離）
                figures = self._create_merged_operation_charts(chart_type, x_intervals, sample_rate)

                # 檢查圖表是否成功生成
                if not figures or len(figures) == 0:
                    print("❌ 圖表生成失敗")
                    self.ui.show_message("圖表生成失敗！")
                    return

                print(f"✅ 生成了 {len(figures)} 個合併操作類型圖表")

                # 🔧 處理多圖表的HTML生成
                self._create_multi_chart_html(figures, chart_type, x_intervals, sample_rate)
                return

            # 生成 HTML 文件
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            html_file = os.path.join(temp_dir, f"fio_analysis_modular_{timestamp}.html")
            
            # 配置
            config = {
                'scrollZoom': True,
                'displayModeBar': True,
                'displaylogo': False,
                'responsive': True,
                'doubleClick': 'reset',
                'selectdirection': 'horizontal',  # 只允許水平選擇（時間軸）
                'dragmode': 'select',             # 默認選擇模式
                'modeBarButtonsToAdd': ['pan2d', 'select2d', 'lasso2d', 'autoScale2d', 'resetScale2d'],
                'toImageButtonOptions': {
                    'format': 'png',
                    'filename': 'fio_analysis_chart',
                    'height': 600,
                    'width': 1000,
                    'scale': 2
                }
            }
            
            # 使用增強版 HTML 模板生成器，包含完整分析功能
            self.ui.html_generator.create_enhanced_html_with_analysis(
                fig, html_file, config, self.ui.all_data,
                [os.path.basename(f) for f in self.ui.logs]
            )
            self.ui.current_plot_file = html_file
            
            # 打開瀏覽器
            webbrowser.open(f'file://{html_file}')

            self.ui.status_label.config(text="🌐 互動式圖表已在瀏覽器中開啟")
            
        except Exception as e:
            logger.error(f"生成圖表錯誤: {str(e)}")
            self.ui.show_message(f"生成圖表時發生錯誤: {str(e)}")
    
    def _create_dual_axis_chart(self, chart_type, x_intervals, sample_rate):
        """創建操作類型分離對比圖表（返回多個圖表）"""
        left_file = self.ui.left_axis_file_var.get()
        right_file = self.ui.right_axis_file_var.get()

        if not left_file or not right_file:
            self.ui.show_message("請選擇左軸和右軸的文件！")
            return None

        # 找到對應的數據
        left_data = None
        right_data = None

        for i, log_file in enumerate(self.ui.logs):
            file_name = os.path.basename(log_file)
            if file_name == left_file and i < len(self.ui.all_data):
                left_data = self.ui.all_data[i]
            if file_name == right_file and i < len(self.ui.all_data):
                right_data = self.ui.all_data[i]

        if left_data is None or right_data is None:
            self.ui.show_message("找不到對應的數據！請確保已載入數據。")
            return None

        # 🔧 使用新的操作類型分離對比圖表生成器
        figures = self.ui.chart_generator.create_operation_comparison_charts(
            left_data, right_data, left_file, right_file,
            self.ui.start_time, self.ui.end_time, sample_rate
        )

        # 🔧 返回多個圖表而不是單個圖表
        return figures

    def _create_merged_operation_charts(self, chart_type, x_intervals, sample_rate):
        """創建合併模式的操作類型分離圖表"""
        if not self.ui.all_data:
            return []

        # 🔧 檢測所有文件中存在的操作類型
        all_operations = set()
        for data in self.ui.all_data:
            operations = self.ui.chart_generator._detect_operations(data)
            all_operations.update(operations)

        all_operations = sorted(list(all_operations))
        print(f"🔧 合併模式操作類型檢測: {all_operations}")

        if not all_operations:
            return []

        figures = []
        operation_names = {0: 'Read', 1: 'Write', 2: 'Trim'}
        operation_colors = {0: '#2E86AB', 1: '#A23B72', 2: '#F18F01'}

        # 為每個操作類型創建獨立圖表
        for op in all_operations:
            op_name = operation_names[op]
            print(f"🔧 創建 {op_name} 合併圖表")

            fig = self._create_single_merged_operation_chart(
                op, op_name, operation_colors[op],
                chart_type, x_intervals, sample_rate
            )
            figures.append(fig)

        return figures

    def _create_single_merged_operation_chart(self, operation, op_name, color, chart_type, x_intervals, sample_rate):
        """創建單個操作類型的合併圖表"""
        from plotly.subplots import make_subplots
        import plotly.graph_objects as go
        import polars as pl

        # 創建圖表
        fig = make_subplots(
            rows=1, cols=1,
            subplot_titles=[f"{op_name} 操作合併分析"],
            specs=[[{"secondary_y": False}]]
        )

        operation_icons = {0: '📖', 1: '✏️', 2: '✂️'}
        icon = operation_icons.get(operation, '📊')

        # 🔧 新增：收集所有文件的數據段，用於虛線連接
        all_file_segments = []
        file_colors = self.ui.chart_generator.colors

        for i, (data, file_name) in enumerate(zip(self.ui.all_data, [os.path.basename(f) for f in self.ui.logs])):
            # 檢查該文件是否有此操作類型
            file_operations = self.ui.chart_generator._detect_operations(data)
            if operation not in file_operations:
                print(f"   文件 {file_name}: ❌無{op_name}數據")
                continue

            print(f"   文件 {file_name}: ✅有{op_name}數據")

            # 過濾該操作類型的數據
            filtered_data = data.filter(pl.col('operation') == operation)
            if filtered_data.height == 0:
                continue

            # 轉換為pandas
            df_pandas = filtered_data.to_pandas()

            # 排序和採樣
            df_pandas = df_pandas.sort_values('time_sec').reset_index(drop=True)
            if sample_rate > 1:
                df_pandas = df_pandas.iloc[::sample_rate]

            # 提取數據
            x_data = df_pandas['time_sec'].tolist()
            y_data = df_pandas['value'].tolist()

            # 使用文件特定的顏色，但保持操作類型的色調
            file_color = file_colors[i % len(file_colors)]

            # 🔧 新增：記錄數據段信息
            if x_data and y_data:
                all_file_segments.append({
                    'file_index': i,
                    'file_name': file_name,
                    'x_data': x_data,
                    'y_data': y_data,
                    'color': file_color,
                    'start_time': min(x_data),
                    'end_time': max(x_data),
                    'start_value': y_data[0],
                    'end_value': y_data[-1]
                })

            # 添加實線軌跡
            trace = go.Scatter(
                x=x_data,
                y=y_data,
                mode='lines',
                name=f'{icon} {file_name}',
                line=dict(color=file_color, width=1.5),
                hovertemplate=f'<b>{icon} {op_name} - {file_name}</b><br>時間: %{{x:.2f}}s<br>值: %{{y:.2f}}<extra></extra>'
            )
            fig.add_trace(trace)

        # 🔧 新增：添加虛線連接不同文件間的空白
        self._add_dashed_connections(fig, all_file_segments, icon, op_name)

        # 設置布局
        fig.update_layout(
            title=f"{icon} {op_name}操作合併分析<br>所有文件的{op_name}操作數據",
            xaxis_title="時間 (秒)",
            yaxis_title="性能值",
            hovermode='x unified',
            template='plotly_white',
            height=400,  # 減少高度，因為會有多個圖表
            showlegend=True,
            legend=dict(
                orientation="v",
                yanchor="top",
                y=0.98,
                xanchor="left",
                x=1.02,
                bgcolor="rgba(255,255,255,0.9)",
                bordercolor="rgba(0,0,0,0.3)",
                borderwidth=1,
                itemclick="toggle",
                itemdoubleclick="toggleothers"
            ),
            margin=dict(l=60, r=200, t=100, b=80),
            xaxis=dict(
                rangeslider=dict(
                    visible=True,
                    autorange=True
                ),
                showgrid=True,
                gridwidth=1,
                gridcolor='lightgray',
                autorange=True
            ),
            yaxis=dict(
                showgrid=True,
                gridwidth=1,
                gridcolor='lightgray',
                autorange=True,
                fixedrange=True
            )
        )

        return fig

    def _add_dashed_connections(self, fig, all_file_segments, icon, op_name):
        """添加虛線連接不同文件間的空白"""
        import plotly.graph_objects as go

        if len(all_file_segments) < 2:
            return  # 少於兩個數據段，無需連接

        # 按時間順序排序數據段
        sorted_segments = sorted(all_file_segments, key=lambda x: x['start_time'])

        print(f"🔧 {op_name}操作虛線連接: 找到 {len(sorted_segments)} 個數據段")

        # 🔧 修改：檢查相鄰數據段之間是否需要連接（包括同一時間點的不同文件）
        for i in range(len(sorted_segments) - 1):
            current_segment = sorted_segments[i]
            next_segment = sorted_segments[i + 1]

            # 計算時間間隔
            time_gap = next_segment['start_time'] - current_segment['end_time']

            # 🔧 修改：只要是不同文件就連接，不管時間間隔多少（包括0秒或負數）
            if current_segment['file_name'] != next_segment['file_name']:
                print(f"   連接 {current_segment['file_name']} -> {next_segment['file_name']}: "
                      f"間隔 {time_gap:.2f}s")

                # 創建虛線連接
                connection_x = [current_segment['end_time'], next_segment['start_time']]
                connection_y = [current_segment['end_value'], next_segment['start_value']]

                # 🔧 修改：使用更顯眼但好看的虛線顏色
                dash_color = self._get_vibrant_dash_color(current_segment['color'])

                # 添加虛線軌跡
                dash_trace = go.Scatter(
                    x=connection_x,
                    y=connection_y,
                    mode='lines',
                    name=f'🔗 連接線 ({current_segment["file_name"]} → {next_segment["file_name"]})',
                    line=dict(
                        color=dash_color,
                        width=2.0,  # 🔧 修改：稍微加粗虛線，更顯眼
                        dash='dash'  # 虛線樣式
                    ),
                    hovertemplate=f'<b>🔗 {op_name}操作連接線</b><br>'
                                f'從: {current_segment["file_name"]}<br>'
                                f'到: {next_segment["file_name"]}<br>'
                                f'時間: %{{x:.2f}}s<br>'
                                f'值: %{{y:.2f}}<br>'
                                f'<i>此為虛線連接，非真實數據</i><extra></extra>',
                    showlegend=True,
                    legendgroup=f'connections_{op_name}',  # 將所有連接線歸為一組
                    opacity=0.8  # 🔧 修改：提高透明度，更顯眼
                )
                fig.add_trace(dash_trace)

    def _get_lighter_color(self, color):
        """獲取較淺的顏色用於虛線（保留舊方法以防其他地方使用）"""
        # 簡單的顏色映射，將原色調整為較淺版本
        color_map = {
            '#1f77b4': '#7fb3d3',  # 藍色 -> 淺藍色
            '#ff7f0e': '#ffb366',  # 橙色 -> 淺橙色
            '#2ca02c': '#7dd87d',  # 綠色 -> 淺綠色
            '#d62728': '#e67e7e',  # 紅色 -> 淺紅色
            '#9467bd': '#c4a4d6',  # 紫色 -> 淺紫色
            '#8c564b': '#b8877a',  # 棕色 -> 淺棕色
            '#e377c2': '#f0b3d9',  # 粉色 -> 淺粉色
            '#7f7f7f': '#b3b3b3',  # 灰色 -> 淺灰色
            '#bcbd22': '#d4d56b',  # 橄欖色 -> 淺橄欖色
            '#17becf': '#6dd4e0'   # 青色 -> 淺青色
        }

        return color_map.get(color, '#cccccc')  # 默認返回淺灰色

    def _get_vibrant_dash_color(self, color):
        """🔧 新增：獲取顯眼但好看的虛線顏色"""
        # 使用更鮮豔但協調的顏色作為虛線顏色
        vibrant_color_map = {
            '#1f77b4': '#FF6B35',  # 藍色 -> 活力橙色
            '#ff7f0e': '#8E44AD',  # 橙色 -> 深紫色
            '#2ca02c': '#E74C3C',  # 綠色 -> 鮮紅色
            '#d62728': '#3498DB',  # 紅色 -> 亮藍色
            '#9467bd': '#F39C12',  # 紫色 -> 金黃色
            '#8c564b': '#1ABC9C',  # 棕色 -> 青綠色
            '#e377c2': '#2ECC71',  # 粉色 -> 翠綠色
            '#7f7f7f': '#E67E22',  # 灰色 -> 橙色
            '#bcbd22': '#9B59B6',  # 橄欖色 -> 紫色
            '#17becf': '#C0392B'   # 青色 -> 深紅色
        }

        return vibrant_color_map.get(color, '#FF4757')  # 默認返回鮮豔的紅色

    def _create_multi_chart_html(self, figures, chart_type, x_intervals, sample_rate):
        """創建多圖表HTML文件"""
        try:
            # 生成 HTML 文件
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            html_file = os.path.join(temp_dir, f"fio_multi_chart_{timestamp}.html")

            # 配置
            config = {
                'scrollZoom': True,
                'displayModeBar': True,
                'displaylogo': False,
                'responsive': True,
                'doubleClick': 'reset',
                'selectdirection': 'horizontal',
                'dragmode': 'select',
                'modeBarButtonsToAdd': ['pan2d', 'select2d', 'lasso2d', 'autoScale2d', 'resetScale2d'],
                'toImageButtonOptions': {
                    'format': 'png',
                    'filename': 'fio_analysis_chart',
                    'height': 400,  # 減少高度因為有多個圖表
                    'width': 1000,
                    'scale': 2
                }
            }

            # 🔧 使用新的多圖表HTML生成器
            self.ui.html_generator.create_multi_chart_html(
                figures, html_file, config
            )
            self.ui.current_plot_file = html_file

            # 打開瀏覽器
            webbrowser.open(f'file://{html_file}')

            self.ui.status_label.config(text=f"🌐 {len(figures)}個操作類型對比圖表已在瀏覽器中開啟")

        except Exception as e:
            logger.error(f"生成多圖表錯誤: {str(e)}")
            self.ui.show_message(f"生成多圖表時發生錯誤: {str(e)}")

    # 導出功能
    def export_plot(self):
        """導出圖表 - 與互動式圖表功能一致"""
        if not self.ui.all_data:
            self.ui.show_message("請先載入數據！")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="導出圖表",
                defaultextension=".html",
                filetypes=[
                    ("HTML files", "*.html"),
                    ("PNG images", "*.png"),
                    ("PDF files", "*.pdf"),
                    ("SVG files", "*.svg"),
                    ("JSON data", "*.json")
                ]
            )

            if not file_path:
                return

            self.ui.status_label.config(text="導出圖表中...")

            # 獲取設置
            try:
                x_intervals = int(self.ui.x_intervals_var.get())
            except:
                x_intervals = 20

            try:
                sample_rate = int(self.ui.sample_var.get())
            except:
                sample_rate = 1

            chart_type = self.ui.chart_type_var.get()
            chart_mode = self.ui.chart_mode_var.get()

            # 檢查是否為雙軸模式
            if chart_mode == "雙軸對比模式":
                fig = self._create_dual_axis_chart(chart_type, x_intervals, sample_rate)
            else:
                # 原有的合併模式
                fig = self.ui.chart_generator.create_interactive_chart(
                    data_list=self.ui.all_data,
                    file_names=[os.path.basename(f) for f in self.ui.logs],
                    file_ranges=self.ui.file_time_ranges,
                    max_time=self.ui.max_time,
                    start_time=self.ui.start_time,
                    end_time=self.ui.end_time,
                    chart_type=chart_type,
                    x_intervals=x_intervals,
                    sample_rate=sample_rate
                )

            # 檢查圖表是否成功生成
            if fig is None:
                self.ui.show_message("圖表生成失敗！")
                return

            # 根據文件擴展名導出
            ext = os.path.splitext(file_path)[1].lower()

            if ext == '.html':
                # 使用與互動式圖表相同的增強版本
                config = {
                    'scrollZoom': True,  # 啟用滾輪縮放
                    'displayModeBar': True,
                    'displaylogo': False,
                    'responsive': True,
                    'doubleClick': 'reset',
                    'selectdirection': 'horizontal',  # 只允許水平選擇（時間軸）
                    'dragmode': 'select',             # 默認選擇模式
                    'modeBarButtonsToAdd': ['pan2d', 'select2d', 'lasso2d', 'autoScale2d', 'resetScale2d'],
                    'toImageButtonOptions': {
                        'format': 'png',
                        'filename': 'fio_analysis_chart',
                        'height': 600,
                        'width': 1000,
                        'scale': 2
                    }
                }

                # 使用增強版本，包含完整分析功能
                self.ui.html_generator.create_enhanced_html_with_analysis(
                    fig, file_path, config, self.ui.all_data,
                    [os.path.basename(f) for f in self.ui.logs]
                )

            elif ext == '.json':
                fig.write_json(file_path)
            elif ext in ['.png', '.pdf', '.svg']:
                fig.write_image(file_path, format=ext[1:], width=1600, height=800)

            self.ui.status_label.config(text="圖表導出完成")

        except Exception as e:
            logger.error(f"導出圖表錯誤: {str(e)}")
            self.ui.show_message(f"導出圖表時發生錯誤: {str(e)}")
    
    def update_plot(self):
        """更新圖表"""
        self.open_interactive_plot()
    
    def analyze_selected_range_threaded(self):
        """分析功能已移至瀏覽器端"""
        self.ui.show_message("📊 分析功能已移至瀏覽器端！\n\n"
                            "請點擊「開啟互動式圖表」使用完整分析功能：\n"
                            "• 百分比範圍選擇\n"
                            "• 拖拽選擇分析區間\n"
                            "• 即時數據分析\n"
                            "• 統計報告生成\n"
                            "• 分析結果導出")

    # 以下分析方法已移至瀏覽器端，保留空實現以避免錯誤

    def analyze_full_range(self):
        """分析功能已移至瀏覽器端"""
        self.analyze_selected_range_threaded()

    def export_analysis_results(self):
        """導出功能已移至瀏覽器端"""
        self.ui.show_message("📊 導出功能已移至瀏覽器端！\n\n"
                            "請點擊「開啟互動式圖表」，在瀏覽器中：\n"
                            "1. 選擇分析範圍\n"
                            "2. 點擊「分析選定範圍」\n"
                            "3. 點擊「導出分析結果」\n\n"
                            "支援多種格式導出：TXT、CSV、JSON")

    # 分析顯示方法已移至瀏覽器端，不再需要

def main():
    """主函數"""
    # 檢查依賴
    if not check_and_install_dependencies():
        return
    
    # 創建主視窗
    root = tk.Tk()
    
    # 設定視窗圖標（如果有的話）
    try:
        root.iconbitmap('icon.ico')  # 可選
    except:
        pass
    
    # 創建應用程序
    app = FioLogAnalyzer(root)
    
    # 設定關閉事件
    def on_closing():
        try:
            # 清理臨時文件
            if hasattr(app.ui, 'current_plot_file') and app.ui.current_plot_file:
                if os.path.exists(app.ui.current_plot_file):
                    os.remove(app.ui.current_plot_file)
        except:
            pass
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 啟動應用程序
    root.mainloop()

if __name__ == "__main__":
    main()
